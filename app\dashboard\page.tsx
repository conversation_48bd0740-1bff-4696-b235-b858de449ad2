import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/lib/auth-config';
import { redirect } from 'next/navigation';
import Image from 'next/image';
import LogoutButton from '@/app/components/LogoutButton';
import ProviderIcon from '@/app/components/ProviderIcon';

export default async function DashboardPage() {
  // Get session using NextAuth
  const session = await getServerSession(authOptions);

  // Redirect to login if not authenticated
  if (!session?.user) {
    redirect('/login');
  }

  const user = session.user;

  return (
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="border-4 border-dashed border-gray-200 rounded-lg p-6">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold">Dashboard</h1>
              <div className="flex items-center gap-3">
                <ProviderIcon
                  provider={user.provider}
                  size={24}
                  className="opacity-75"
                />
                <LogoutButton />
              </div>
            </div>
            <p className="mb-4">Welcome, {user.name}!</p>
            <div className="bg-gray-50 p-4 rounded-md">
              <h2 className="text-lg font-medium mb-2">Your Profile</h2>
              <ul className="space-y-2">
                <li><strong>Name:</strong> {user.name}</li>
                <li><strong>Email:</strong> {user.email}</li>
                {user.image && (
                  <li className="flex items-center space-x-2">
                    <strong>Profile Image:</strong>
                    <Image
                      src={user.image}
                      alt={`Profile picture of ${user.name || 'User'}`}
                      width={64}
                      height={64}
                      className="rounded-full"
                    />
                  </li>
                )}
                {user.emailVerified && (
                  <li><strong>Email Verified:</strong> {user.emailVerified.toLocaleDateString()}</li>
                )}
                {user.provider && (
                  <li><strong>Sign-in Provider:</strong> {user.provider}</li>
                )}
              </ul>
            </div>
          </div>
        </div>
      </div>
  );
}
