import { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import GitHubProvider from 'next-auth/providers/github';
import EmailProvider from 'next-auth/providers/email';
import { PrismaAdapter } from '@next-auth/prisma-adapter';
import { prisma } from './prisma';
import { sendVerificationRequest } from './email';



const googleClientId = process.env.GOOGLE_CLIENT_ID || '';
const googleClientSecret = process.env.GOOGLE_CLIENT_SECRET || '';
const githubClientId = process.env.GITHUB_ID || '';
const githubClientSecret = process.env.GITHUB_SECRET || '';
const resendApiKey = process.env.RESEND_API_KEY || '';
const resendSender = process.env.RESEND_SENDER || '';

if (!googleClientId || !googleClientSecret) {
  throw new Error('Missing Google OAuth credentials');
}

if (!githubClientId || !githubClientSecret) {
  throw new Error('Missing GitHub OAuth credentials');
}

if (!resendApiKey || !resendSender) {
  throw new Error('Missing Resend email credentials');
}

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    GoogleProvider({
      clientId: googleClientId,
      clientSecret: googleClientSecret,
    }),
    GitHubProvider({
      clientId: githubClientId,
      clientSecret: githubClientSecret,
    }),
    EmailProvider({
      server: {
        host: 'smtp.resend.com',
        port: 587,
        auth: {
          user: 'resend',
          pass: resendApiKey,
        },
      },
      from: resendSender,
      sendVerificationRequest,
      // Extend magic link expiration to 24 hours
      maxAge: 24 * 60 * 60, // 24 hours in seconds
    }),
  ],
  session: {
    strategy: 'database',
    // Extend session duration to 30 days
    maxAge: 30 * 24 * 60 * 60, // 30 days in seconds
    // Update session every 24 hours
    updateAge: 24 * 60 * 60, // 24 hours in seconds
  },
  callbacks: {
    async session({ session, user }) {
      // Include user ID in session (simplified)
      if (session.user && user) {
        session.user.id = user.id;
      }
      return session;
    },
  },
};
