import { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import GitHubProvider from 'next-auth/providers/github';
import EmailProvider from 'next-auth/providers/email';
import { PrismaAdapter } from '@next-auth/prisma-adapter';
import { prisma } from './prisma';
import { sendVerificationRequest } from './email';



const googleClientId = process.env.GOOGLE_CLIENT_ID || '';
const googleClientSecret = process.env.GOOGLE_CLIENT_SECRET || '';
const githubClientId = process.env.GITHUB_ID || '';
const githubClientSecret = process.env.GITHUB_SECRET || '';
const resendApiKey = process.env.RESEND_API_KEY || '';
const resendSender = process.env.RESEND_SENDER || '';

if (!googleClientId || !googleClientSecret) {
  throw new Error('Missing Google OAuth credentials');
}

if (!githubClientId || !githubClientSecret) {
  throw new Error('Missing GitHub OAuth credentials');
}

if (!resendApiKey || !resendSender) {
  throw new Error('Missing Resend email credentials');
}

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    GoogleProvider({
      clientId: googleClientId,
      clientSecret: googleClientSecret,
    }),
    GitHubProvider({
      clientId: githubClientId,
      clientSecret: githubClientSecret,
    }),
    EmailProvider({
      server: {
        host: 'smtp.resend.com',
        port: 587,
        auth: {
          user: 'resend',
          pass: resendApiKey,
        },
      },
      from: resendSender,
      sendVerificationRequest,
      // Extend magic link expiration to 24 hours
      maxAge: 24 * 60 * 60, // 24 hours in seconds
    }),
  ],
  session: {
    strategy: 'database',
    // Extend session duration to 30 days
    maxAge: 30 * 24 * 60 * 60, // 30 days in seconds
    // Update session every 24 hours
    updateAge: 24 * 60 * 60, // 24 hours in seconds
  },
  pages: {
    signIn: '/login',
    error: '/login',
    verifyRequest: '/login', // Redirect here after email is sent
  },
  callbacks: {
    async session({ session, user }) {
      // Debug session callback
      console.log('NextAuth session callback:', {
        hasSession: !!session,
        hasUser: !!user,
        sessionUserEmail: session?.user?.email,
        userEmail: user?.email
      });

      // Include user ID in session (simplified)
      if (session.user && user) {
        session.user.id = user.id;
      }
      return session;
    },
    async redirect({ url, baseUrl }) {
      // Handle magic link redirects properly
      console.log('NextAuth redirect callback - url:', url, 'baseUrl:', baseUrl);

      // If coming from magic link verification, redirect to dashboard
      if (url.includes('/api/auth/callback/email')) {
        console.log('Magic link callback detected, redirecting to dashboard');
        return `${baseUrl}/dashboard`;
      }

      // For other internal URLs, allow them
      if (url.startsWith(baseUrl)) {
        return url;
      }

      // Default to dashboard for external URLs
      return `${baseUrl}/dashboard`;
    },
  },
};
