'use client';

import { SessionProvider } from 'next-auth/react';
import { useEffect } from 'react';

function ChunkErrorHandler({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Handle chunk load errors by refreshing the page
    const handleChunkError = (event: ErrorEvent) => {
      if (event.message?.includes('Loading chunk') ||
          event.message?.includes('ChunkLoadError')) {
        console.log('Chunk load error detected, refreshing page...');
        window.location.reload();
      }
    };

    // Handle unhandled promise rejections (chunk errors)
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      if (event.reason?.message?.includes('Loading chunk') ||
          event.reason?.name === 'ChunkLoadError') {
        console.log('Chunk load error detected, refreshing page...');
        window.location.reload();
      }
    };

    window.addEventListener('error', handleChunkError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleChunkError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return <>{children}</>;
}

export function NextAuthProvider({ children }: { children: React.ReactNode }) {
  return (
    <ChunkErrorHandler>
      <SessionProvider
        // Refetch session every 5 minutes
        refetchInterval={5 * 60}
        // Refetch session when window gains focus
        refetchOnWindowFocus={true}
      >
        {children}
      </SessionProvider>
    </ChunkErrorHandler>
  );
}