'use client';

import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { signIn, useSession } from 'next-auth/react';

export default function AuthCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session, status } = useSession();

  useEffect(() => {
    const handleCallback = async () => {
      // If already authenticated, redirect to dashboard
      if (status === 'authenticated' && session?.user) {
        router.replace('/dashboard');
        return;
      }

      // Check if this is an email verification callback
      const token = searchParams.get('token');
      const email = searchParams.get('email');
      
      if (token && email) {
        try {
          // Let NextAuth handle the email verification
          const result = await signIn('email', {
            email,
            token,
            redirect: false,
          });

          if (result?.ok) {
            // Success - redirect to dashboard
            router.replace('/dashboard');
          } else {
            // Error - redirect to login with error
            router.replace('/login?error=verification_failed');
          }
        } catch (error) {
          console.error('Email verification error:', error);
          router.replace('/login?error=verification_failed');
        }
      } else {
        // No token/email, redirect to login
        router.replace('/login');
      }
    };

    // Only run if session status is determined
    if (status !== 'loading') {
      handleCallback();
    }
  }, [status, session, router, searchParams]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Verifying your sign-in...</p>
      </div>
    </div>
  );
}
